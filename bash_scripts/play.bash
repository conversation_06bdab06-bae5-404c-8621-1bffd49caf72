cd ~/IsaacLab

# ./isaaclab.sh -p scripts/reinforcement_learning/skrl/play.py \
# --task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-Franka-v0 \
# --num_envs 1 \
# --checkpoint logs/skrl/franka_lift/2025-07-29_11-46-36_ppo_torch/checkpoints/agent_36000.pt 

# ./isaaclab.sh -p scripts/reinforcement_learning/skrl/play.py \
# --task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-Ruirman-v0 \
# --num_envs 9 \
# --checkpoint logs/skrl/ruirman_lift/2025-06-25_16-50-22_ppo_torch/checkpoints/agent_50000.pt 

# ./isaaclab.sh -p scripts/reinforcement_learning/skrl/play.py \
# --task <PERSON>-Lift-Cube-ZLZK-v0 \
# --num_envs 1 \
# --checkpoint logs/skrl/zlzk_lift/2025-07-09_11-14-05_ppo_torch/checkpoints/agent_100000.pt 


./isaaclab.sh -p scripts/reinforcement_learning/rl_games/play.py \
--task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-Shadow-Vision-Direct-Play-v0 \
--enable_cameras \

