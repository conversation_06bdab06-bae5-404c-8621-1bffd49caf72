services:
  isaac-lab-base:
    environment:
      - DISPLAY
      - TERM
      - QT_X11_NO_MITSHM=1
      - XAUTHORITY=${__ISAACLAB_TMP_XAUTH}
    volumes:
    - type: bind
      source: ${__ISAACLAB_TMP_DIR}
      target: ${__ISAACLAB_TMP_DIR}
    - type: bind
      source: /tmp/.X11-unix
      target: /tmp/.X11-unix
    - type: bind
      source: /etc/localtime
      target: /etc/localtime
      read_only: true

  isaac-lab-ros2:
    environment:
      - DISPLAY
      - TERM
      - QT_X11_NO_MITSHM=1
      - XAUTHORITY=${__ISAACLAB_TMP_XAUTH}
    volumes:
    - type: bind
      source: ${__ISAACLAB_TMP_DIR}
      target: ${__ISAACLAB_TMP_DIR}
    - type: bind
      source: /tmp/.X11-unix
      target: /tmp/.X11-unix
    - type: bind
      source: /etc/localtime
      target: /etc/localtime
      read_only: true
