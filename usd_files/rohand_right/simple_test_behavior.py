#!/usr/bin/env python3
"""
简单的 BehaviorScript 测试脚本
用于验证脚本是否正常工作和日志输出
"""

from omni.kit.scripting import BehaviorScript
import carb
import datetime
import sys

class SimpleTestBehavior(BehaviorScript):
    """简单的测试行为脚本"""
    
    def __init__(self):
        super().__init__()
        self._frame_count = 0
        self._log_file = "/tmp/behavior_test.log"
        
    def _log_message(self, message):
        """统一的日志输出方法"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        full_message = f"[{timestamp}] {message}"
        
        # 多种输出方式确保能看到
        print(full_message)
        carb.log_info(full_message)
        sys.stdout.flush()
        
        # 写入文件
        try:
            with open(self._log_file, "a") as f:
                f.write(full_message + "\n")
        except:
            pass
    
    def on_init(self):
        """初始化"""
        self._log_message("✓ SimpleTestBehavior: on_init() called")
        
    def on_destroy(self):
        """销毁"""
        self._log_message("✓ SimpleTestBehavior: on_destroy() called")
        
    def on_play(self):
        """开始播放"""
        self._log_message("✓ SimpleTestBehavior: on_play() called")
        self._frame_count = 0
        
    def on_pause(self):
        """暂停"""
        self._log_message("✓ SimpleTestBehavior: on_pause() called")
        
    def on_stop(self):
        """停止"""
        self._log_message("✓ SimpleTestBehavior: on_stop() called")
        
    def on_update(self):
        """每帧更新"""
        self._frame_count += 1
        
        # 每60帧（约1秒）输出一次
        if self._frame_count % 60 == 0:
            self._log_message(f"✓ SimpleTestBehavior: on_update() frame {self._frame_count}")
            
        # 前10帧每帧都输出，用于测试
        elif self._frame_count <= 10:
            self._log_message(f"✓ SimpleTestBehavior: on_update() frame {self._frame_count}")
