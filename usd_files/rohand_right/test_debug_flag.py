from omni.kit.scripting import BehaviorScript
import carb

class TestDebugFlag(BehaviorScript):
    
    def on_init(self):
        # 测试 debug_flag = True 的情况
        self.debug_flag = True
        
        if self.debug_flag:
            print("=== TestDebugFlag: on_init called (debug_flag=True) ===")
    
    def on_play(self):
        if self.debug_flag:
            print("=== TestDebugFlag: on_play called (debug_flag=True) ===")
        
        # 在运行时切换 debug_flag 来测试
        # 5秒后关闭调试输出
        import asyncio
        asyncio.ensure_future(self._test_debug_toggle())
    
    async def _test_debug_toggle(self):
        """测试动态切换 debug_flag"""
        import omni.kit.app
        
        # 等待5秒
        for i in range(300):  # 约5秒（假设60fps）
            await omni.kit.app.get_app().next_update_async()
        
        # 关闭调试输出
        self.debug_flag = False
        print("=== Debug flag turned OFF - no more debug messages ===")
        
        # 再等待3秒
        for i in range(180):  # 约3秒
            await omni.kit.app.get_app().next_update_async()
        
        # 重新开启调试输出
        self.debug_flag = True
        print("=== Debug flag turned ON - debug messages resumed ===")
    
    def on_pause(self):
        if self.debug_flag:
            print("=== TestDebugFlag: on_pause called ===")
    
    def on_stop(self):
        if self.debug_flag:
            print("=== TestDebugFlag: on_stop called ===")
    
    def on_destroy(self):
        if self.debug_flag:
            print("=== TestDebugFlag: on_destroy called ===")
