# 手部关节联动更新频率调整指南

## 概述

`rohand_joint_linkage.py` 现在支持灵活的更新频率控制，可以根据实际需要调整手部关节联动的计算频率。

## 当前实现

### 默认设置
- **默认频率**: 30Hz（每秒30次更新）
- **渲染帧率**: 60FPS（Isaac Sim 默认）
- **实际执行**: 每2帧执行一次关节联动计算

### 频率控制机制
```python
# 在 _joint_linkage_loop() 中
self._update_frequency_hz = 30  # 可调整的更新频率
self._frame_skip = max(1, int(60 / self._update_frequency_hz))  # 自动计算跳帧数

# 只在指定帧执行计算
if self._update_counter % self._frame_skip == 0:
    self._update_hand_joint_linkage()
```

## 频率选择建议

### 🚀 60Hz - 最高频率
- **适用场景**: 需要极高精度的实时控制
- **特点**: 每帧都执行，最高精度
- **缺点**: 计算资源消耗最大
- **推荐用于**: 精密的机器人控制、实时仿真

### ⚡ 30Hz - 高频率（默认）
- **适用场景**: 一般的关节联动应用
- **特点**: 性能和精度的良好平衡
- **优点**: 足够的更新精度，合理的资源消耗
- **推荐用于**: 大多数手部关节联动应用

### 🎯 20Hz - 中等频率
- **适用场景**: 标准的关节联动
- **特点**: 每3帧执行一次
- **优点**: 较低的计算负载，仍保持良好响应
- **推荐用于**: 一般的手部动作模拟

### 💡 10Hz - 低频率
- **适用场景**: 简单的关节联动
- **特点**: 每6帧执行一次
- **优点**: 显著节省计算资源
- **推荐用于**: 非关键的关节状态更新

### 🔋 5Hz - 很低频率
- **适用场景**: 非实时的状态更新
- **特点**: 每12帧执行一次
- **优点**: 最小的计算开销
- **推荐用于**: 状态监控、数据记录

## 使用方法

### 1. 修改默认频率
在 `_joint_linkage_loop()` 方法中修改：
```python
self._update_frequency_hz = 20  # 改为20Hz
```

### 2. 动态调整频率
使用新增的 `set_update_frequency()` 方法：
```python
# 在脚本运行时调用
linkage_script.set_update_frequency(20)  # 设置为20Hz
```

### 3. 根据应用场景选择
```python
# 高精度控制
self._update_frequency_hz = 60

# 一般应用（推荐）
self._update_frequency_hz = 30

# 节省资源
self._update_frequency_hz = 10
```

## 性能影响分析

### 计算负载对比
| 频率 | 每秒计算次数 | 相对负载 | 资源消耗 |
|------|-------------|----------|----------|
| 60Hz | 60次        | 100%     | 最高     |
| 30Hz | 30次        | 50%      | 中等     |
| 20Hz | 20次        | 33%      | 较低     |
| 10Hz | 10次        | 17%      | 低       |
| 5Hz  | 5次         | 8%       | 最低     |

### 响应延迟
| 频率 | 最大延迟 | 平均延迟 | 适用场景 |
|------|----------|----------|----------|
| 60Hz | 16.7ms   | 8.3ms    | 实时控制 |
| 30Hz | 33.3ms   | 16.7ms   | 一般应用 |
| 20Hz | 50ms     | 25ms     | 标准应用 |
| 10Hz | 100ms    | 50ms     | 简单应用 |
| 5Hz  | 200ms    | 100ms    | 状态更新 |

## 测试和调优

### 使用测试脚本
运行 `test_update_frequency.py` 来：
- 测试不同频率的实际性能
- 观察计算负载的差异
- 选择最适合的频率设置

### 性能监控
观察以下指标来选择合适的频率：
- CPU使用率
- 帧率稳定性
- 关节联动的响应性
- 整体系统性能

## 实际应用建议

### 开发阶段
- 使用较高频率（30-60Hz）进行开发和调试
- 确保关节联动逻辑正确

### 生产环境
- 根据实际需求选择合适的频率
- 在性能和精度之间找到平衡点

### 特殊场景
- **演示/展示**: 使用30-60Hz确保流畅性
- **批量仿真**: 使用10-20Hz节省资源
- **数据采集**: 使用5-10Hz即可满足需求

## 注意事项

1. **频率限制**: 最高频率不能超过渲染帧率（通常60FPS）
2. **计算复杂度**: 关节联动计算越复杂，建议使用越低的频率
3. **系统性能**: 在性能较低的系统上应适当降低频率
4. **应用需求**: 根据实际的精度要求选择合适的频率
