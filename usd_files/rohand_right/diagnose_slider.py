from omni.kit.scripting import BehaviorScript
import omni.usd

class DiagnoseSlider(BehaviorScript):
    
    def on_init(self):
        self.debug_flag = True
        self._test_counter = 0
        
        if self.debug_flag:
            print("=== DiagnoseSlider: on_init called ===")
            print("This script will help diagnose slider joint position reading issues")
    
    def on_play(self):
        if self.debug_flag:
            print("=== DiagnoseSlider: on_play called ===")
            print("Please manually set if_slider_link to position 0.010m")
            print("Then observe the diagnostic output...")
    
    def on_update(self):
        self._test_counter += 1
        
        # 每3秒进行一次详细诊断
        if self._test_counter % 180 == 0:
            self._diagnose_all_sliders()
    
    def on_stop(self):
        if self.debug_flag:
            print("=== DiagnoseSlider: on_stop called ===")
    
    def on_destroy(self):
        if self.debug_flag:
            print("=== DiagnoseSlider: on_destroy called ===")
    
    def _diagnose_all_sliders(self):
        """诊断所有slider关节"""
        try:
            stage = omni.usd.get_context().get_stage()
            if not stage:
                print("❌ No USD stage found")
                return
            
            print(f"\n{'='*60}")
            print(f"SLIDER JOINT DIAGNOSTIC REPORT (Frame {self._test_counter})")
            print(f"{'='*60}")
            
            # 要诊断的slider关节
            slider_joints = [
                'if_slider_link',   # 食指slider - 重点测试这个
                'th_slider_link',   # 拇指slider
                'mf_slider_link',   # 中指slider
                'rf_slider_link',   # 无名指slider
                'lf_slider_link',   # 小指slider
            ]
            
            for joint_name in slider_joints:
                print(f"\n--- 诊断 {joint_name} ---")
                self._diagnose_single_slider(stage, joint_name)
            
            print(f"\n{'='*60}")
            print("DIAGNOSTIC COMPLETE")
            print("Please check the output above to find the correct attribute names")
            print(f"{'='*60}\n")
            
        except Exception as e:
            print(f"❌ Error in diagnostic: {e}")
            import traceback
            traceback.print_exc()
    
    def _diagnose_single_slider(self, stage, joint_name):
        """诊断单个slider关节"""
        try:
            # 1. 查找所有相关的prim
            matching_prims = []
            for prim in stage.Traverse():
                prim_name = prim.GetName()
                prim_path = str(prim.GetPath())
                
                # 查找包含关节名称的prim
                if joint_name in prim_name or joint_name in prim_path:
                    matching_prims.append((prim, prim_name, prim_path))
            
            if not matching_prims:
                print(f"  ❌ No prims found matching '{joint_name}'")
                
                # 尝试模糊搜索
                print(f"  🔍 Searching for similar names...")
                similar_prims = []
                search_terms = joint_name.replace('_', '').lower()
                for prim in stage.Traverse():
                    prim_name = prim.GetName().replace('_', '').lower()
                    if any(term in prim_name for term in search_terms.split()):
                        similar_prims.append((prim.GetName(), str(prim.GetPath())))
                
                if similar_prims:
                    print(f"  📋 Found similar prims:")
                    for name, path in similar_prims[:5]:  # 只显示前5个
                        print(f"    - {name} at {path}")
                else:
                    print(f"  ❌ No similar prims found")
                return
            
            print(f"  ✅ Found {len(matching_prims)} matching prim(s):")
            for prim, name, path in matching_prims:
                print(f"    - {name} at {path}")
            
            # 2. 分析每个匹配的prim
            for i, (prim, prim_name, prim_path) in enumerate(matching_prims):
                print(f"\n  📊 Analyzing prim #{i+1}: {prim_name}")
                print(f"     Path: {prim_path}")
                print(f"     Type: {prim.GetTypeName()}")
                
                # 获取所有属性
                all_attributes = []
                for attr in prim.GetAttributes():
                    attr_name = attr.GetName()
                    attr_value = attr.Get()
                    all_attributes.append((attr_name, attr_value))
                
                print(f"     Total attributes: {len(all_attributes)}")
                
                # 3. 查找位置相关的属性
                position_attrs = []
                transform_attrs = []
                drive_attrs = []
                physics_attrs = []
                other_attrs = []
                
                for attr_name, attr_value in all_attributes:
                    attr_lower = attr_name.lower()
                    
                    if "position" in attr_lower:
                        position_attrs.append((attr_name, attr_value))
                    elif "translate" in attr_lower or "transform" in attr_lower:
                        transform_attrs.append((attr_name, attr_value))
                    elif "drive" in attr_lower:
                        drive_attrs.append((attr_name, attr_value))
                    elif "physics" in attr_lower:
                        physics_attrs.append((attr_name, attr_value))
                    else:
                        other_attrs.append((attr_name, attr_value))
                
                # 4. 显示分类的属性
                if position_attrs:
                    print(f"     🎯 Position attributes ({len(position_attrs)}):")
                    for attr_name, attr_value in position_attrs:
                        print(f"       * {attr_name}: {attr_value}")
                
                if transform_attrs:
                    print(f"     🔄 Transform attributes ({len(transform_attrs)}):")
                    for attr_name, attr_value in transform_attrs:
                        print(f"       * {attr_name}: {attr_value}")
                
                if drive_attrs:
                    print(f"     ⚙️  Drive attributes ({len(drive_attrs)}):")
                    for attr_name, attr_value in drive_attrs:
                        print(f"       * {attr_name}: {attr_value}")
                
                if physics_attrs:
                    print(f"     🔬 Physics attributes ({len(physics_attrs)}):")
                    for attr_name, attr_value in physics_attrs[:5]:  # 只显示前5个
                        print(f"       * {attr_name}: {attr_value}")
                    if len(physics_attrs) > 5:
                        print(f"       ... and {len(physics_attrs) - 5} more physics attributes")
                
                # 5. 如果没有找到明显的位置属性，显示所有属性
                if not position_attrs and not transform_attrs and not drive_attrs:
                    print(f"     📋 All attributes (first 10):")
                    for attr_name, attr_value in all_attributes[:10]:
                        print(f"       * {attr_name}: {attr_value}")
                    if len(all_attributes) > 10:
                        print(f"       ... and {len(all_attributes) - 10} more attributes")
                
                # 6. 尝试读取可能的位置值
                print(f"     🧪 Testing position reading:")
                test_attrs = [
                    "physics:position", "drive:linear:physics:targetPosition",
                    "state:linear:physics:position", "xformOp:translate"
                ]
                
                found_value = False
                for test_attr in test_attrs:
                    if prim.HasAttribute(test_attr):
                        try:
                            attr = prim.GetAttribute(test_attr)
                            value = attr.Get()
                            print(f"       ✅ {test_attr}: {value}")
                            found_value = True
                        except Exception as e:
                            print(f"       ❌ {test_attr}: Error - {e}")
                
                if not found_value:
                    print(f"       ❌ No standard position attributes found")
                
        except Exception as e:
            print(f"  ❌ Error diagnosing {joint_name}: {e}")
            import traceback
            traceback.print_exc()
