from omni.kit.scripting import BehaviorScript
import omni.usd

class TestSliderPosition(BehaviorScript):
    
    def on_init(self):
        self.debug_flag = True
        self._test_counter = 0
        
        if self.debug_flag:
            print("=== TestSliderPosition: on_init called ===")
    
    def on_play(self):
        if self.debug_flag:
            print("=== TestSliderPosition: on_play called ===")
    
    def on_update(self):
        # 简单的测试，每60帧检查一次slider位移
        self._test_counter += 1
        
        if self._test_counter % 60 == 0:  # 每秒检查一次
            self._test_slider_positions()
    
    def on_stop(self):
        if self.debug_flag:
            print("=== TestSliderPosition: on_stop called ===")
    
    def on_destroy(self):
        if self.debug_flag:
            print("=== TestSliderPosition: on_destroy called ===")
    
    def _test_slider_positions(self):
        """测试读取所有slider关节的位移"""
        try:
            stage = omni.usd.get_context().get_stage()
            if not stage:
                print("No USD stage found")
                return
            
            # 定义要测试的slider关节名称
            slider_joints = [
                'th_slider_link',           # 拇指slider
                'if_slider_link',           # 食指slider
                'mf_slider_link',           # 中指slider
                'rf_slider_link',           # 无名指slider
                'lf_slider_link',           # 小指slider
            ]
            
            print(f"\n=== Slider Position Test (Frame {self._test_counter}) ===")
            
            for joint_name in slider_joints:
                position = self._get_joint_position(stage, joint_name)
                print(f"{joint_name}: {position:.6f}m")
            
        except Exception as e:
            print(f"Error in slider position test: {e}")
    
    def _get_joint_position(self, stage, joint_name):
        """读取指定 slider 关节的当前位移"""
        try:
            # 在舞台中查找关节
            joint_prim = None
            for prim in stage.Traverse():
                if prim.GetName() == joint_name:
                    joint_prim = prim
                    break
            
            if joint_prim:
                print(f"  Found joint: {joint_name}")
                
                # 列出所有属性以便调试
                if self.debug_flag and self._test_counter == 60:  # 只在第一次输出
                    print(f"    Available attributes:")
                    for attr in joint_prim.GetAttributes():
                        attr_name = attr.GetName()
                        attr_value = attr.Get()
                        print(f"      {attr_name}: {attr_value}")
                
                # 尝试从不同的属性中读取关节位移
                # 方法1: 尝试读取 physics:position 属性（线性位移）
                if joint_prim.HasAttribute("physics:position"):
                    pos_attr = joint_prim.GetAttribute("physics:position")
                    value = pos_attr.Get()
                    print(f"    physics:position: {value}")
                    return float(value or 0.0)
                
                # 方法2: 尝试读取 drive:linear:physics:targetPosition
                if joint_prim.HasAttribute("drive:linear:physics:targetPosition"):
                    target_attr = joint_prim.GetAttribute("drive:linear:physics:targetPosition")
                    value = target_attr.Get()
                    print(f"    drive:linear:physics:targetPosition: {value}")
                    return float(value or 0.0)
                
                # 方法3: 尝试读取变换位移信息
                if joint_prim.HasAttribute("xformOp:translate"):
                    trans_attr = joint_prim.GetAttribute("xformOp:translate")
                    translation = trans_attr.Get()
                    if translation:
                        print(f"    xformOp:translate: {translation}")
                        # 假设是沿 X 轴滑动（根据实际情况调整为 Y 或 Z 轴）
                        return float(translation[0])
                
                # 方法4: 尝试读取关节状态位移
                if joint_prim.HasAttribute("state:linear:physics:position"):
                    state_attr = joint_prim.GetAttribute("state:linear:physics:position")
                    value = state_attr.Get()
                    print(f"    state:linear:physics:position: {value}")
                    return float(value or 0.0)
                
                # 方法5: 检查所有包含 "position" 的属性
                for attr in joint_prim.GetAttributes():
                    attr_name = attr.GetName()
                    if "position" in attr_name.lower():
                        value = attr.Get()
                        print(f"    Found position attribute {attr_name}: {value}")
                        if value is not None:
                            try:
                                return float(value)
                            except:
                                if hasattr(value, '__len__') and len(value) > 0:
                                    return float(value[0])
                
                print(f"    No position attribute found for {joint_name}")
            else:
                print(f"  Joint not found: {joint_name}")
            
            return 0.0  # 默认返回0
            
        except Exception as e:
            print(f"Error reading joint position for {joint_name}: {e}")
            return 0.0
