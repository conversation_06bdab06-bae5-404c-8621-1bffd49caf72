from omni.kit.scripting import BehaviorScript
import carb
import traceback
import sys
import datetime

class CatchError(BehaviorScript):
    
    def __init__(self):
        # 不调用 super().__init__()，看看是否这是问题
        self.update_count = 0
        self.error_logged = False
        
    def _safe_log(self, message):
        """安全的日志记录方法"""
        try:
            timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
            full_msg = f"[{timestamp}] CATCH_ERROR: {message}"
            print(full_msg)
            carb.log_info(full_msg)
            
            # 写入文件
            with open("/tmp/catch_error.log", "a") as f:
                f.write(full_msg + "\n")
                f.flush()  # 强制写入
        except Exception as e:
            # 如果日志记录失败，至少尝试 print
            try:
                print(f"LOG ERROR: {e}")
            except:
                pass
    
    def _log_exception(self, method_name, e):
        """记录异常的安全方法"""
        try:
            error_info = {
                'method': method_name,
                'exception_type': type(e).__name__,
                'exception_message': str(e),
                'traceback': traceback.format_exc()
            }
            
            error_msg = f"EXCEPTION in {method_name}:\n"
            error_msg += f"  Type: {error_info['exception_type']}\n"
            error_msg += f"  Message: {error_info['exception_message']}\n"
            error_msg += f"  Traceback:\n{error_info['traceback']}"
            
            print("=" * 60)
            print(error_msg)
            print("=" * 60)
            
            carb.log_error(error_msg)
            
            # 写入错误文件
            with open("/tmp/catch_error_exceptions.log", "a") as f:
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")
                f.write(f"\n[{timestamp}] {error_msg}\n")
                f.flush()
                
        except Exception as log_error:
            print(f"CRITICAL: Cannot log exception: {log_error}")
    
    def on_init(self):
        try:
            self._safe_log("on_init called")
        except Exception as e:
            self._log_exception("on_init", e)
    
    def on_destroy(self):
        try:
            self._safe_log("on_destroy called")
        except Exception as e:
            self._log_exception("on_destroy", e)
    
    def on_play(self):
        try:
            self._safe_log("on_play called")
            self.update_count = 0
            self.error_logged = False
        except Exception as e:
            self._log_exception("on_play", e)
    
    def on_pause(self):
        try:
            self._safe_log("on_pause called")
        except Exception as e:
            self._log_exception("on_pause", e)
    
    def on_stop(self):
        try:
            self._safe_log("on_stop called")
        except Exception as e:
            self._log_exception("on_stop", e)
    
    def on_update(self):
        try:
            self.update_count += 1
            
            # 前5次更新都记录
            if self.update_count <= 5:
                self._safe_log(f"on_update called #{self.update_count}")
            
            # 每100次记录一次
            elif self.update_count % 100 == 0:
                self._safe_log(f"on_update called #{self.update_count}")
            
            # 测试一些可能导致错误的操作
            if self.update_count == 3:
                self._safe_log("Testing potentially problematic operations...")
                
                # 测试1: 访问不存在的属性
                # test_var = self.non_existent_attribute  # 这会导致 AttributeError
                
                # 测试2: 除零错误
                # result = 1 / 0  # 这会导致 ZeroDivisionError
                
                # 测试3: 类型错误
                # result = "string" + 123  # 这会导致 TypeError
                
                self._safe_log("All tests passed in on_update")
                
        except Exception as e:
            if not self.error_logged:  # 只记录第一次错误，避免刷屏
                self._log_exception("on_update", e)
                self.error_logged = True
            
            # 重新抛出异常，让系统知道发生了错误
            raise
