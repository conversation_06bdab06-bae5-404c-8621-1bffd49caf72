#!/usr/bin/env python3
"""
简单的位置读取测试脚本
在 Isaac Sim Script Editor 中运行
"""

import omni.usd

def test_slider_position_reading():
    """测试slider位置读取"""
    print("=== Slider Position Reading Test ===")
    
    try:
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ No USD stage found")
            return
        
        # 要测试的slider关节
        test_joint = 'if_slider_link'  # 食指slider
        
        print(f"🔍 Searching for joint: {test_joint}")
        
        # 方法1: 精确匹配
        exact_match = None
        for prim in stage.Traverse():
            if prim.GetName() == test_joint:
                exact_match = prim
                break
        
        if exact_match:
            print(f"✅ Found exact match: {exact_match.GetPath()}")
            analyze_prim_for_position(exact_match)
        else:
            print(f"❌ No exact match found for {test_joint}")
            
            # 方法2: 模糊匹配
            print(f"🔍 Searching for similar names...")
            similar_prims = []
            for prim in stage.Traverse():
                prim_name = prim.GetName()
                if 'if_' in prim_name and 'slider' in prim_name:
                    similar_prims.append(prim)
            
            if similar_prims:
                print(f"📋 Found {len(similar_prims)} similar prim(s):")
                for prim in similar_prims:
                    print(f"  - {prim.GetName()} at {prim.GetPath()}")
                    analyze_prim_for_position(prim)
            else:
                print(f"❌ No similar prims found")
                
                # 方法3: 列出所有包含 'slider' 的prim
                print(f"🔍 Listing all prims containing 'slider'...")
                slider_prims = []
                for prim in stage.Traverse():
                    prim_name = prim.GetName()
                    if 'slider' in prim_name.lower():
                        slider_prims.append(prim)
                
                if slider_prims:
                    print(f"📋 Found {len(slider_prims)} slider prim(s):")
                    for prim in slider_prims[:10]:  # 只显示前10个
                        print(f"  - {prim.GetName()} at {prim.GetPath()}")
                else:
                    print(f"❌ No slider prims found at all")
    
    except Exception as e:
        print(f"❌ Error in test: {e}")
        import traceback
        traceback.print_exc()

def analyze_prim_for_position(prim):
    """分析prim的位置相关属性"""
    try:
        print(f"\n📊 Analyzing: {prim.GetName()} ({prim.GetPath()})")
        print(f"   Type: {prim.GetTypeName()}")
        
        # 获取所有属性
        all_attrs = []
        for attr in prim.GetAttributes():
            attr_name = attr.GetName()
            try:
                attr_value = attr.Get()
                all_attrs.append((attr_name, attr_value))
            except:
                all_attrs.append((attr_name, "Error reading value"))
        
        print(f"   Total attributes: {len(all_attrs)}")
        
        # 查找位置相关属性
        position_related = []
        for attr_name, attr_value in all_attrs:
            attr_lower = attr_name.lower()
            if any(keyword in attr_lower for keyword in ['position', 'translate', 'transform', 'drive', 'target']):
                position_related.append((attr_name, attr_value))
        
        if position_related:
            print(f"   🎯 Position-related attributes ({len(position_related)}):")
            for attr_name, attr_value in position_related:
                # 特别标记可能有用的属性
                marker = "⭐" if any(keyword in attr_name.lower() for keyword in ['targetposition', 'position', 'translate']) else "  "
                print(f"   {marker} {attr_name}: {attr_value}")
        else:
            print(f"   ❌ No position-related attributes found")
            print(f"   📋 First 10 attributes:")
            for attr_name, attr_value in all_attrs[:10]:
                print(f"     - {attr_name}: {attr_value}")
        
        # 尝试读取常见的位置属性
        print(f"   🧪 Testing common position attributes:")
        test_attrs = [
            "drive:linear:physics:targetPosition",
            "physics:position", 
            "state:linear:physics:position",
            "xformOp:translate",
            "xformOp:transform",
            "drive:angular:physics:targetPosition",  # 也测试角度属性
            "physics:angle"
        ]
        
        for test_attr in test_attrs:
            if prim.HasAttribute(test_attr):
                try:
                    attr = prim.GetAttribute(test_attr)
                    value = attr.Get()
                    print(f"   ✅ {test_attr}: {value}")
                except Exception as e:
                    print(f"   ❌ {test_attr}: Error reading - {e}")
            else:
                print(f"   ⚪ {test_attr}: Not found")
        
    except Exception as e:
        print(f"   ❌ Error analyzing prim: {e}")

def list_all_joints():
    """列出所有可能的关节"""
    print("\n=== Listing All Possible Joints ===")
    
    try:
        stage = omni.usd.get_context().get_stage()
        if not stage:
            print("❌ No USD stage found")
            return
        
        joint_keywords = ['joint', 'link', 'slider', 'revolute', 'prismatic']
        joints = []
        
        for prim in stage.Traverse():
            prim_name = prim.GetName()
            prim_type = prim.GetTypeName()
            
            # 查找可能的关节
            if any(keyword in prim_name.lower() for keyword in joint_keywords) or \
               any(keyword in prim_type.lower() for keyword in ['joint', 'physics']):
                joints.append((prim_name, prim_type, str(prim.GetPath())))
        
        if joints:
            print(f"📋 Found {len(joints)} potential joints:")
            for name, type_name, path in joints:
                print(f"  - {name} ({type_name}) at {path}")
        else:
            print("❌ No joints found")
    
    except Exception as e:
        print(f"❌ Error listing joints: {e}")

if __name__ == "__main__":
    print("🚀 Starting slider position diagnostic...")
    test_slider_position_reading()
    list_all_joints()
    print("✅ Diagnostic complete!")

# 如果在 Isaac Sim Script Editor 中运行，直接执行
try:
    test_slider_position_reading()
    list_all_joints()
except:
    pass
