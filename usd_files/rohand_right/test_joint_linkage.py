#!/usr/bin/env python3
"""
测试手部关节联动功能
"""

def test_joint_linkage_functions():
    """测试关节联动计算函数"""
    print("=== 测试手部关节联动计算函数 ===")
    
    try:
        # 导入必要的模块
        import sys
        import os
        script_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, script_dir)
        
        from rohand_joint_linkage import (
            HAND_FingerPosToAngle,
            THUMB_ID, INDEX_FINGER_ID, MIDDLE_FINGER_ID, 
            RING_FINGER_ID, LITTLE_FINGER_ID, THUMB_ROOT_ID,
            JOINTS_NAME
        )
        
        print("✓ 成功导入关节联动函数")
        
        # 测试不同手指的关节联动计算
        test_positions = [0.0, 0.005, 0.01, 0.015, 0.02]  # 不同的slider位置
        finger_ids = [THUMB_ID, INDEX_FINGER_ID, MIDDLE_FINGER_ID, RING_FINGER_ID, LITTLE_FINGER_ID, THUMB_ROOT_ID]
        finger_names = ["拇指", "食指", "中指", "无名指", "小指", "拇指根部"]
        
        for pos in test_positions:
            print(f"\n--- 测试 slider 位置: {pos:.3f}m ---")
            
            for i, finger_id in enumerate(finger_ids):
                try:
                    angles = HAND_FingerPosToAngle(finger_id, pos)
                    joint_names = JOINTS_NAME[finger_id] if finger_id < len(JOINTS_NAME) else ["unknown"]
                    
                    print(f"{finger_names[i]} (ID:{finger_id}):")
                    print(f"  关节名称: {joint_names}")
                    print(f"  计算角度: {angles}")
                    
                    if angles:
                        if isinstance(angles, list):
                            print(f"  角度数量: {len(angles)}")
                            for j, angle in enumerate(angles):
                                print(f"    角度[{j}]: {angle:.4f} rad ({math.degrees(angle):.2f}°)")
                        else:
                            print(f"  单个角度: {angles:.4f} rad ({math.degrees(angles):.2f}°)")
                    
                except Exception as e:
                    print(f"  ❌ 计算失败: {e}")
        
        print("\n✓ 关节联动计算函数测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_joint_mapping():
    """测试关节名称映射"""
    print("\n=== 测试关节名称映射 ===")
    
    try:
        from rohand_joint_linkage import (
            JOINTS_NAME, THUMB_ID, INDEX_FINGER_ID, MIDDLE_FINGER_ID,
            RING_FINGER_ID, LITTLE_FINGER_ID, THUMB_ROOT_ID
        )
        
        finger_ids = [THUMB_ID, INDEX_FINGER_ID, MIDDLE_FINGER_ID, RING_FINGER_ID, LITTLE_FINGER_ID, THUMB_ROOT_ID]
        finger_names = ["拇指", "食指", "中指", "无名指", "小指", "拇指根部"]
        
        for i, finger_id in enumerate(finger_ids):
            if finger_id < len(JOINTS_NAME):
                joint_names = JOINTS_NAME[finger_id]
                print(f"\n{finger_names[i]} (ID:{finger_id}):")
                print(f"  关节数量: {len(joint_names)}")
                
                for j, joint_name in enumerate(joint_names):
                    joint_type = "unknown"
                    if "slider" in joint_name:
                        joint_type = "slider (输入关节)"
                    elif "proximal" in joint_name:
                        joint_type = "proximal (近端关节)"
                    elif "distal" in joint_name:
                        joint_type = "distal (远端关节)"
                    elif "connecting" in joint_name:
                        joint_type = "connecting (连接关节)"
                    elif "abpart" in joint_name:
                        joint_type = "abpart (外展关节)"
                    elif "root" in joint_name:
                        joint_type = "root (根部关节)"
                    
                    print(f"    [{j}] {joint_name} - {joint_type}")
        
        print("\n✓ 关节名称映射测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 关节映射测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n=== 生成手部关节联动测试报告 ===")
    
    try:
        import math
        from rohand_joint_linkage import (
            HAND_FingerPosToAngle, JOINTS_NAME,
            THUMB_ID, INDEX_FINGER_ID, MIDDLE_FINGER_ID, 
            RING_FINGER_ID, LITTLE_FINGER_ID, THUMB_ROOT_ID
        )
        
        report_file = "/tmp/hand_joint_linkage_test_report.txt"
        
        with open(report_file, "w", encoding="utf-8") as f:
            f.write("手部关节联动测试报告\n")
            f.write("=" * 50 + "\n\n")
            
            finger_ids = [THUMB_ID, INDEX_FINGER_ID, MIDDLE_FINGER_ID, RING_FINGER_ID, LITTLE_FINGER_ID, THUMB_ROOT_ID]
            finger_names = ["拇指", "食指", "中指", "无名指", "小指", "拇指根部"]
            test_positions = [0.0, 0.005, 0.01, 0.015, 0.02]
            
            for pos in test_positions:
                f.write(f"Slider 位置: {pos:.3f}m\n")
                f.write("-" * 30 + "\n")
                
                for i, finger_id in enumerate(finger_ids):
                    try:
                        angles = HAND_FingerPosToAngle(finger_id, pos)
                        joint_names = JOINTS_NAME[finger_id] if finger_id < len(JOINTS_NAME) else ["unknown"]
                        
                        f.write(f"\n{finger_names[i]} (ID:{finger_id}):\n")
                        f.write(f"  关节名称: {joint_names}\n")
                        
                        if angles:
                            if isinstance(angles, list):
                                f.write(f"  角度数量: {len(angles)}\n")
                                for j, angle in enumerate(angles):
                                    f.write(f"    关节[{j}] {joint_names[j] if j < len(joint_names) else 'unknown'}: {angle:.4f} rad ({math.degrees(angle):.2f}°)\n")
                            else:
                                f.write(f"  角度: {angles:.4f} rad ({math.degrees(angles):.2f}°)\n")
                        else:
                            f.write("  无角度数据\n")
                            
                    except Exception as e:
                        f.write(f"  错误: {e}\n")
                
                f.write("\n" + "=" * 50 + "\n\n")
        
        print(f"✓ 测试报告已生成: {report_file}")
        return True
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
        return False

if __name__ == "__main__":
    import math
    
    print("开始手部关节联动功能测试...")
    
    # 运行所有测试
    test1 = test_joint_linkage_functions()
    test2 = test_joint_mapping()
    test3 = generate_test_report()
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"关节联动计算: {'✓ 通过' if test1 else '❌ 失败'}")
    print(f"关节名称映射: {'✓ 通过' if test2 else '❌ 失败'}")
    print(f"测试报告生成: {'✓ 通过' if test3 else '❌ 失败'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 所有测试通过！手部关节联动功能已准备就绪。")
        print("\n使用说明:")
        print("1. 在 Isaac Sim 中加载 rohand_joint_linkage.py")
        print("2. 将脚本附加到手部模型")
        print("3. 点击 Play 开始关节联动")
        print("4. 手动调整 slider 关节，观察其他关节的自动联动")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
