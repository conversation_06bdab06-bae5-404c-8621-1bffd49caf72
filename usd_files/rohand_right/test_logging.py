#!/usr/bin/env python3
"""
简单的日志测试脚本，用于验证 Omniverse 中的日志输出
"""

import carb
import datetime
import os

def test_logging():
    """测试各种日志输出方法"""
    
    print("=== 开始日志测试 ===")
    
    # 1. 测试 print 输出
    print("1. 这是 print 输出")
    
    # 2. 测试 carb 日志输出
    carb.log_info("2. 这是 carb.log_info 输出")
    carb.log_warn("3. 这是 carb.log_warn 输出")
    carb.log_error("4. 这是 carb.log_error 输出")
    
    # 3. 测试文件输出
    log_file = "/tmp/omniverse_test.log"
    try:
        with open(log_file, "a") as f:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"{timestamp}: 5. 这是文件日志输出\n")
        print(f"6. 文件日志已写入: {log_file}")
    except Exception as e:
        print(f"文件写入失败: {e}")
    
    # 4. 测试标准错误输出
    import sys
    sys.stderr.write("7. 这是标准错误输出\n")
    sys.stderr.flush()
    
    print("=== 日志测试完成 ===")
    print(f"请检查以下位置的输出:")
    print("- Omniverse Console 窗口")
    print("- 终端输出")
    print(f"- 文件: {log_file}")

if __name__ == "__main__":
    test_logging()
