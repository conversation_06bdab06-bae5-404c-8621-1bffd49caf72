#!/usr/bin/env python3
"""
测试最终的手部关节联动解决方案
"""

def test_hand_joint_functions():
    """测试手部关节计算函数是否正常工作"""
    
    print("=== 测试手部关节计算函数 ===")
    
    try:
        # 导入你的脚本中的函数
        import sys
        import os
        
        # 添加脚本路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, script_dir)
        
        # 导入手部关节计算函数
        from rohand_joint_linkage import (
            THUMB_OffsetToAngle, 
            FINGER_OffsetToAngle, 
            HAND_FingerPosToAngle,
            NUM_FINGERS
        )
        
        print("✓ 成功导入手部关节计算函数")
        
        # 测试拇指角度计算
        print("\n--- 测试拇指角度计算 ---")
        thumb_offset = 0.01  # 1cm 偏移
        thumb_angles = THUMB_OffsetToAngle(thumb_offset)
        print(f"拇指偏移 {thumb_offset}m 的角度: {thumb_angles}")
        
        # 测试其他手指角度计算
        print("\n--- 测试其他手指角度计算 ---")
        for finger_id in range(1, NUM_FINGERS):
            finger_offset = 0.005  # 0.5cm 偏移
            finger_angles = FINGER_OffsetToAngle(finger_id, finger_offset)
            print(f"手指 {finger_id} 偏移 {finger_offset}m 的角度: {finger_angles}")
        
        # 测试综合函数
        print("\n--- 测试综合函数 ---")
        for finger_id in range(NUM_FINGERS + 1):  # 包括额外的电机
            pos = 0.008  # 0.8cm 位置
            angles = HAND_FingerPosToAngle(finger_id, pos)
            print(f"手指 {finger_id} 位置 {pos}m 的角度: {angles}")
        
        print("\n✓ 所有手部关节计算函数测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 手部关节计算函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_behavior_script():
    """测试 BehaviorScript 是否正常工作"""
    
    print("\n=== 测试 BehaviorScript ===")
    
    try:
        from omni.kit.scripting import BehaviorScript
        from rohand_joint_linkage import HandJointLinkage
        
        print("✓ 成功导入 BehaviorScript 和 HandJointLinkage")
        
        # 创建实例
        linkage = HandJointLinkage()
        print("✓ 成功创建 HandJointLinkage 实例")
        
        # 测试生命周期方法
        linkage.on_init()
        print("✓ on_init 方法正常")
        
        linkage.on_play()
        print("✓ on_play 方法正常")
        
        linkage.on_pause()
        print("✓ on_pause 方法正常")
        
        linkage.on_stop()
        print("✓ on_stop 方法正常")
        
        linkage.on_destroy()
        print("✓ on_destroy 方法正常")
        
        print("✓ BehaviorScript 测试通过")
        return True
        
    except Exception as e:
        print(f"✗ BehaviorScript 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试最终解决方案...")
    
    # 测试计算函数
    functions_ok = test_hand_joint_functions()
    
    # 测试 BehaviorScript
    behavior_ok = test_behavior_script()
    
    print("\n=== 测试结果 ===")
    print(f"手部关节计算函数: {'✓ 通过' if functions_ok else '✗ 失败'}")
    print(f"BehaviorScript: {'✓ 通过' if behavior_ok else '✗ 失败'}")
    
    if functions_ok and behavior_ok:
        print("\n🎉 所有测试通过！你的手部关节联动脚本已经修复并可以正常使用了。")
        print("\n使用说明：")
        print("1. 在 Isaac Sim 中将 rohand_joint_linkage.py 附加到你的手部模型")
        print("2. 点击 Play 开始手部关节联动")
        print("3. 在 _update_hand_joint_linkage() 方法中添加你的具体联动逻辑")
    else:
        print("\n❌ 部分测试失败，请检查错误信息并修复。")
