from omni.kit.scripting import BehaviorScript
import carb
import traceback

class SimpleCatch(BehaviorScript):
    # 完全不使用自定义 __init__
    
    def on_init(self):
        try:
            print("SIMPLE_CATCH: on_init")
            carb.log_info("SIMPLE_CATCH: on_init")
            
            # 初始化一些实例变量
            self.update_count = 0
            self.error_count = 0
            
        except Exception as e:
            error_msg = f"SIMPLE_CATCH on_init ERROR: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
    
    def on_play(self):
        try:
            print("SIMPLE_CATCH: on_play")
            carb.log_info("SIMPLE_CATCH: on_play")
            
            # 重置计数器
            if hasattr(self, 'update_count'):
                self.update_count = 0
            else:
                self.update_count = 0
                
            if hasattr(self, 'error_count'):
                self.error_count = 0
            else:
                self.error_count = 0
                
        except Exception as e:
            error_msg = f"SIMPLE_CATCH on_play ERROR: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
    
    def on_update(self):
        try:
            # 安全地增加计数器
            if hasattr(self, 'update_count'):
                self.update_count += 1
            else:
                self.update_count = 1

            # 前10次更新都打印，确保我们能看到
            if self.update_count <= 10:
                print(f"SIMPLE_CATCH: on_update #{self.update_count} - UPDATE IS WORKING!")
                carb.log_info(f"SIMPLE_CATCH: on_update #{self.update_count}")

                # 写入文件确保记录
                try:
                    with open("/tmp/simple_catch_updates.log", "a") as f:
                        import datetime
                        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
                        f.write(f"[{timestamp}] on_update #{self.update_count}\n")
                except:
                    pass

            # 每50次打印一次（降低频率）
            elif self.update_count % 50 == 0:
                print(f"SIMPLE_CATCH: on_update #{self.update_count}")
                carb.log_info(f"SIMPLE_CATCH: on_update #{self.update_count}")

        except Exception as e:
            # 安全地增加错误计数器
            if hasattr(self, 'error_count'):
                self.error_count += 1
            else:
                self.error_count = 1

            # 记录所有错误
            error_msg = f"SIMPLE_CATCH on_update ERROR #{self.error_count}: {e}\n{traceback.format_exc()}"
            print("=" * 60)
            print("CRITICAL ERROR IN ON_UPDATE:")
            print(error_msg)
            print("=" * 60)
            carb.log_error(error_msg)

            # 写入文件
            try:
                with open("/tmp/simple_catch_error.log", "a") as f:
                    import datetime
                    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    f.write(f"[{timestamp}] {error_msg}\n\n")
            except:
                pass

            # 重新抛出异常让系统知道
            raise
    
    def on_pause(self):
        try:
            print("SIMPLE_CATCH: on_pause")
            carb.log_info("SIMPLE_CATCH: on_pause")
        except Exception as e:
            error_msg = f"SIMPLE_CATCH on_pause ERROR: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
    
    def on_stop(self):
        try:
            print("SIMPLE_CATCH: on_stop")
            carb.log_info("SIMPLE_CATCH: on_stop")
        except Exception as e:
            error_msg = f"SIMPLE_CATCH on_stop ERROR: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
    
    def on_destroy(self):
        try:
            print("SIMPLE_CATCH: on_destroy")
            carb.log_info("SIMPLE_CATCH: on_destroy")
        except Exception as e:
            error_msg = f"SIMPLE_CATCH on_destroy ERROR: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
