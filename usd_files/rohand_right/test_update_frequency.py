from omni.kit.scripting import BehaviorScript
import asyncio

class TestUpdateFrequency(BehaviorScript):
    
    def on_init(self):
        self.debug_flag = True
        self._timer_task = None
        self._is_running = False
        self._test_counter = 0
        
        if self.debug_flag:
            print("=== TestUpdateFrequency: on_init called ===")
    
    def on_play(self):
        if self.debug_flag:
            print("=== TestUpdateFrequency: on_play called ===")
        self._start_frequency_test()
    
    def on_stop(self):
        if self.debug_flag:
            print("=== TestUpdateFrequency: on_stop called ===")
        self._stop_frequency_test()
    
    def on_destroy(self):
        if self.debug_flag:
            print("=== TestUpdateFrequency: on_destroy called ===")
        self._stop_frequency_test()
    
    def _start_frequency_test(self):
        """启动频率测试"""
        if not self._is_running:
            self._is_running = True
            self._test_counter = 0
            self._timer_task = asyncio.ensure_future(self._frequency_test_loop())
            if self.debug_flag:
                print("TestUpdateFrequency: Frequency test started")
    
    def _stop_frequency_test(self):
        """停止频率测试"""
        self._is_running = False
        if self._timer_task:
            self._timer_task.cancel()
            self._timer_task = None
            if self.debug_flag:
                print("TestUpdateFrequency: Frequency test stopped")
    
    async def _frequency_test_loop(self):
        """测试不同更新频率的效果"""
        try:
            import omni.kit.app
            
            # 测试不同的更新频率
            test_frequencies = [
                (60, "60Hz - 最高频率（每帧更新）"),
                (30, "30Hz - 高频率（每2帧更新）"),
                (20, "20Hz - 中等频率（每3帧更新）"),
                (10, "10Hz - 低频率（每6帧更新）"),
                (5, "5Hz - 很低频率（每12帧更新）")
            ]
            
            for freq_hz, description in test_frequencies:
                if not self._is_running:
                    break
                
                print(f"\n=== 测试 {description} ===")
                
                frame_skip = max(1, int(60 / freq_hz))
                frame_count = 0
                update_count = 0
                test_duration = 180  # 测试3秒（约180帧）
                
                start_time = None
                
                for i in range(test_duration):
                    if not self._is_running:
                        break
                    
                    frame_count += 1
                    
                    # 记录开始时间
                    if start_time is None:
                        import time
                        start_time = time.time()
                    
                    # 按照指定频率执行更新
                    if frame_count % frame_skip == 0:
                        update_count += 1
                        
                        # 模拟手部关节联动计算
                        self._simulate_joint_calculation()
                        
                        # 每10次更新显示一次状态
                        if update_count % 10 == 0:
                            elapsed = time.time() - start_time
                            actual_freq = update_count / elapsed if elapsed > 0 else 0
                            print(f"  更新 #{update_count}, 实际频率: {actual_freq:.1f}Hz")
                    
                    await omni.kit.app.get_app().next_update_async()
                
                # 显示测试结果
                if start_time:
                    total_time = time.time() - start_time
                    actual_freq = update_count / total_time if total_time > 0 else 0
                    print(f"  测试完成: 目标频率 {freq_hz}Hz, 实际频率 {actual_freq:.1f}Hz")
                    print(f"  总帧数: {frame_count}, 总更新数: {update_count}")
                
                # 等待1秒再进行下一个测试
                for i in range(60):
                    if not self._is_running:
                        break
                    await omni.kit.app.get_app().next_update_async()
            
            print("\n=== 所有频率测试完成 ===")
            print("建议:")
            print("- 60Hz: 适用于需要极高精度的实时控制")
            print("- 30Hz: 适用于一般的关节联动，性能和精度平衡")
            print("- 20Hz: 适用于大多数手部关节联动应用")
            print("- 10Hz: 适用于简单的关节联动，节省计算资源")
            print("- 5Hz:  适用于非实时的关节状态更新")
                
        except asyncio.CancelledError:
            if self.debug_flag:
                print("TestUpdateFrequency: Test loop cancelled")
        except Exception as e:
            print(f"TestUpdateFrequency: Error in test loop: {e}")
    
    def _simulate_joint_calculation(self):
        """模拟手部关节联动计算"""
        # 模拟一些计算工作
        import math
        result = 0
        for i in range(100):  # 简单的计算循环
            result += math.sin(i * 0.1) * math.cos(i * 0.1)
        return result
