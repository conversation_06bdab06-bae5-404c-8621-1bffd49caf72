from omni.kit.scripting import BehaviorScript
import carb
import traceback
import sys

class MinimalTest(BehaviorScript):

    def on_init(self):
        try:
            print("MINIMAL TEST: on_init")
            carb.log_info("MINIMAL TEST: on_init")
        except Exception as e:
            error_msg = f"Error in on_init: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)

    def on_play(self):
        try:
            print("MINIMAL TEST: on_play")
            carb.log_info("MINIMAL TEST: on_play")
        except Exception as e:
            error_msg = f"Error in on_play: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)

    def on_update(self):
        try:
            print("MINIMAL TEST: on_update")
            carb.log_info("MINIMAL TEST: on_update")
            # 完全空的 on_update - 但添加异常处理
            pass
        except Exception as e:
            error_msg = f"Error in on_update: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
            # 写入文件以便调试
            try:
                with open("/tmp/minimal_test_error.log", "a") as f:
                    f.write(f"{error_msg}\n")
            except:
                pass

    def on_stop(self):
        try:
            print("MINIMAL TEST: on_stop")
            carb.log_info("MINIMAL TEST: on_stop")
        except Exception as e:
            error_msg = f"Error in on_stop: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)

    def on_destroy(self):
        try:
            print("MINIMAL TEST: on_destroy")
            carb.log_info("MINIMAL TEST: on_destroy")
        except Exception as e:
            error_msg = f"Error in on_destroy: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
