from omni.kit.scripting import BehaviorScript
import carb

class MinimalTest(BehaviorScript):
    
    def on_init(self):
        print("MINIMAL TEST: on_init")
        carb.log_info("MINIMAL TEST: on_init")
        
    def on_play(self):
        print("MINIMAL TEST: on_play")
        carb.log_info("MINIMAL TEST: on_play")
        
    def on_update(self):
        # 完全空的 on_update
        pass
        
    def on_stop(self):
        print("MINIMAL TEST: on_stop")
        carb.log_info("MINIMAL TEST: on_stop")
        
    def on_destroy(self):
        print("MINIMAL TEST: on_destroy")
        carb.log_info("MINIMAL TEST: on_destroy")
