#!/usr/bin/env python3
"""
专门用于调试 BehaviorScript 错误的脚本
"""

from omni.kit.scripting import BehaviorScript
import carb
import traceback
import datetime
import sys
import os


class DebugBehavior(BehaviorScript):
    """专门用于调试的 BehaviorScript"""
    
    def __init__(self):
        # 注意：在 BehaviorScript 中通常不需要自定义 __init__
        # 但我们测试一下是否这是问题的原因
        try:
            super().__init__()
            self._log("__init__ called successfully")
        except Exception as e:
            self._log_error("__init__", e)
    
    def _log(self, message):
        """统一的日志方法"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        full_msg = f"[{timestamp}] DEBUG: {message}"
        
        # 多种输出方式
        print(full_msg)
        carb.log_info(full_msg)
        
        # 写入文件
        try:
            with open("/tmp/debug_behavior.log", "a") as f:
                f.write(full_msg + "\n")
        except:
            pass
    
    def _log_error(self, method_name, exception):
        """统一的错误日志方法"""
        error_msg = f"ERROR in {method_name}: {exception}\n{traceback.format_exc()}"
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        full_msg = f"[{timestamp}] {error_msg}"
        
        print(full_msg)
        carb.log_error(full_msg)
        
        # 写入错误文件
        try:
            with open("/tmp/debug_behavior_error.log", "a") as f:
                f.write(full_msg + "\n")
        except:
            pass
    
    def on_init(self):
        try:
            self._log("on_init called")
            # 测试一些可能导致错误的操作
            self._log(f"Python version: {sys.version}")
            self._log(f"Current working directory: {os.getcwd()}")
            self._log("on_init completed successfully")
        except Exception as e:
            self._log_error("on_init", e)
    
    def on_destroy(self):
        try:
            self._log("on_destroy called")
        except Exception as e:
            self._log_error("on_destroy", e)
    
    def on_play(self):
        try:
            self._log("on_play called")
        except Exception as e:
            self._log_error("on_play", e)
    
    def on_pause(self):
        try:
            self._log("on_pause called")
        except Exception as e:
            self._log_error("on_pause", e)
    
    def on_stop(self):
        try:
            self._log("on_stop called")
        except Exception as e:
            self._log_error("on_stop", e)
    
    def on_update(self):
        try:
            # 什么都不做，但记录调用
            pass
        except Exception as e:
            self._log_error("on_update", e)


# 创建一个没有 __init__ 的版本进行对比
class DebugBehaviorNoInit(BehaviorScript):
    """没有自定义 __init__ 的版本"""
    
    def _log(self, message):
        """统一的日志方法"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        full_msg = f"[{timestamp}] DEBUG_NO_INIT: {message}"
        
        print(full_msg)
        carb.log_info(full_msg)
        
        try:
            with open("/tmp/debug_behavior_no_init.log", "a") as f:
                f.write(full_msg + "\n")
        except:
            pass
    
    def on_init(self):
        try:
            self._log("on_init called (no custom __init__)")
        except Exception as e:
            error_msg = f"ERROR in on_init: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
    
    def on_play(self):
        try:
            self._log("on_play called (no custom __init__)")
        except Exception as e:
            error_msg = f"ERROR in on_play: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
    
    def on_update(self):
        try:
            pass
        except Exception as e:
            error_msg = f"ERROR in on_update: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
    
    def on_stop(self):
        try:
            self._log("on_stop called (no custom __init__)")
        except Exception as e:
            error_msg = f"ERROR in on_stop: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
    
    def on_destroy(self):
        try:
            self._log("on_destroy called (no custom __init__)")
        except Exception as e:
            error_msg = f"ERROR in on_destroy: {e}\n{traceback.format_exc()}"
            print(error_msg)
            carb.log_error(error_msg)
