from omni.kit.scripting import BehaviorScript
import carb
import omni.timeline

class EventSolution(BehaviorScript):
    
    def on_init(self):
        print("EVENT_SOLUTION: on_init")
        carb.log_info("EVENT_SOLUTION: on_init")
        self._timeline = omni.timeline.get_timeline_interface()
        self._subscription = None
    
    def on_play(self):
        print("EVENT_SOLUTION: on_play")
        carb.log_info("EVENT_SOLUTION: on_play")
        self._subscribe_to_timeline()
    
    def on_pause(self):
        print("EVENT_SOLUTION: on_pause")
        carb.log_info("EVENT_SOLUTION: on_pause")
    
    def on_stop(self):
        print("EVENT_SOLUTION: on_stop")
        carb.log_info("EVENT_SOLUTION: on_stop")
        self._unsubscribe_from_timeline()
    
    def on_destroy(self):
        print("EVENT_SOLUTION: on_destroy")
        carb.log_info("EVENT_SOLUTION: on_destroy")
        self._unsubscribe_from_timeline()
    
    def _subscribe_to_timeline(self):
        """订阅时间线事件"""
        try:
            if self._timeline and not self._subscription:
                # 订阅时间线更新事件
                timeline_event_stream = self._timeline.get_timeline_event_stream()
                self._subscription = timeline_event_stream.create_subscription_to_pop(
                    self._on_timeline_event, name="hand_joint_linkage"
                )
                print("EVENT_SOLUTION: Subscribed to timeline events")
        except Exception as e:
            print(f"EVENT_SOLUTION: Failed to subscribe to timeline: {e}")
            carb.log_error(f"Timeline subscription error: {e}")
    
    def _unsubscribe_from_timeline(self):
        """取消订阅时间线事件"""
        if self._subscription:
            self._subscription.unsubscribe()
            self._subscription = None
            print("EVENT_SOLUTION: Unsubscribed from timeline events")
    
    def _on_timeline_event(self, event):
        """时间线事件处理 - 替代 on_update 的功能"""
        try:
            # 检查是否是播放状态
            if self._timeline.is_playing():
                current_time = self._timeline.get_current_time()
                
                # 每秒打印一次（假设60fps）
                if int(current_time * 60) % 60 == 0:
                    print(f"EVENT_SOLUTION: Timeline update at {current_time:.2f}s")
                
                # 在这里添加你的手部关节联动逻辑
                self._update_hand_joints(current_time)
                
        except Exception as e:
            print(f"EVENT_SOLUTION: Error in timeline event: {e}")
            carb.log_error(f"Timeline event error: {e}")
    
    def _update_hand_joints(self, current_time):
        """手部关节更新逻辑"""
        # 这里可以添加你原始脚本中的手部关节联动计算
        # 参数 current_time 是当前的时间线时间
        pass
