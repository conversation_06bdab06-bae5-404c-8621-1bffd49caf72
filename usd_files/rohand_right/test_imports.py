from omni.kit.scripting import BehaviorScript
import carb

# 逐步添加你原始脚本中的导入来测试
# from pxr import Usd, UsdPhysics, UsdGeom, Gf
# import math
# import omni.usd
# import omni.timeline
# import numpy as np

class TestImports(BehaviorScript):
    
    def on_init(self):
        print("TEST_IMPORTS: on_init")
        carb.log_info("TEST_IMPORTS: on_init")
    
    def on_play(self):
        print("TEST_IMPORTS: on_play")
        carb.log_info("TEST_IMPORTS: on_play")
    
    def on_update(self):
        pass
    
    def on_stop(self):
        print("TEST_IMPORTS: on_stop")
        carb.log_info("TEST_IMPORTS: on_stop")
    
    def on_destroy(self):
        print("TEST_IMPORTS: on_destroy")
        carb.log_info("TEST_IMPORTS: on_destroy")
