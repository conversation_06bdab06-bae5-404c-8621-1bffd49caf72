{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Attach (windows-x86_64/linux-x86_64)",
            "type": "python",
            "request": "attach",
            "pathMappings": [
            {
                "localRoot": "${workspaceFolder}",
                "remoteRoot": "${workspaceFolder}"
            }],
            "port": 3000,
            "host": "127.0.0.1",
            "subProcess": true,
            "runtimeArgs": [
                "--preserve-symlinks",
                "--preserve-symlinks-main"
            ]
        }
    ]
}
