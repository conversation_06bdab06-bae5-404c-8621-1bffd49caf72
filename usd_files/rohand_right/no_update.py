from omni.kit.scripting import BehaviorScript
import carb

class NoUpdate(BehaviorScript):
    
    def on_init(self):
        print("NO_UPDATE: on_init")
        carb.log_info("NO_UPDATE: on_init")
    
    def on_play(self):
        print("NO_UPDATE: on_play")
        carb.log_info("NO_UPDATE: on_play")
    
    # 故意不定义 on_update 方法
    
    def on_pause(self):
        print("NO_UPDATE: on_pause")
        carb.log_info("NO_UPDATE: on_pause")
    
    def on_stop(self):
        print("NO_UPDATE: on_stop")
        carb.log_info("NO_UPDATE: on_stop")
    
    def on_destroy(self):
        print("NO_UPDATE: on_destroy")
        carb.log_info("NO_UPDATE: on_destroy")
