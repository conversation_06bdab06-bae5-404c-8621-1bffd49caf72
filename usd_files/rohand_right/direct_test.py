#!/usr/bin/env python3
"""
直接测试脚本 - 不使用 BehaviorScript
在 Omniverse Script Editor 中直接运行
"""

import sys
import os
import datetime

def direct_test():
    """直接测试各种输出方法"""
    
    print("=" * 50)
    print("开始直接测试")
    print("=" * 50)
    
    # 1. 基本 print 测试
    print("1. 基本 print 输出测试")
    
    # 2. 标准输出测试
    sys.stdout.write("2. sys.stdout.write 测试\n")
    sys.stdout.flush()
    
    # 3. 标准错误测试
    sys.stderr.write("3. sys.stderr.write 测试\n")
    sys.stderr.flush()
    
    # 4. carb 日志测试
    try:
        import carb
        carb.log_info("4. carb.log_info 测试")
        carb.log_warn("5. carb.log_warn 测试")
        carb.log_error("6. carb.log_error 测试")
        print("✓ carb 日志测试完成")
    except Exception as e:
        print(f"✗ carb 日志测试失败: {e}")
    
    # 5. 文件输出测试
    log_file = "/tmp/direct_test.log"
    try:
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open(log_file, "w") as f:
            f.write(f"{timestamp}: 直接测试文件输出\n")
        print(f"7. 文件输出成功: {log_file}")
        
        # 验证文件内容
        with open(log_file, "r") as f:
            content = f.read()
        print(f"8. 文件内容: {content.strip()}")
        
    except Exception as e:
        print(f"✗ 文件输出失败: {e}")
    
    # 6. 环境信息
    print(f"9. Python 版本: {sys.version}")
    print(f"10. 当前目录: {os.getcwd()}")
    print(f"11. 脚本路径: {__file__ if '__file__' in globals() else '未知'}")
    
    print("=" * 50)
    print("直接测试完成")
    print("=" * 50)

# 直接运行测试
if __name__ == "__main__":
    direct_test()
else:
    # 如果作为模块导入，也运行测试
    direct_test()
