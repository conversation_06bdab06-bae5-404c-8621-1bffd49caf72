# Debug Flag 使用说明

## 概述
`rohand_joint_linkage.py` 脚本现在包含一个 `debug_flag` 功能，可以控制调试信息的输出。

## 功能特点

### ✅ 已实现的功能
- **调试标志控制**：通过 `self.debug_flag` 控制是否输出调试信息
- **保留 print 输出**：所有调试信息使用 `print()` 函数输出
- **移除 carb 日志**：移除了 `carb.log_info()` 调用，只保留错误日志 `carb.log_error()`
- **全面覆盖**：所有生命周期方法和定时器方法都支持调试标志控制

### 🎛️ 控制的日志输出
当 `debug_flag = True` 时，会输出以下信息：
- `on_init()` 调用信息
- `on_play()` 调用信息  
- `on_pause()` 调用信息
- `on_stop()` 调用信息
- `on_destroy()` 调用信息
- 定时器启动/停止信息
- 前5帧的处理信息
- 每60帧的状态信息
- 定时器循环取消信息

当 `debug_flag = False` 时，上述调试信息都不会输出。

### ⚠️ 始终输出的信息
无论 `debug_flag` 设置如何，以下信息始终会输出：
- 所有错误信息（`print()` 和 `carb.log_error()`）
- 异常堆栈跟踪信息

## 使用方法

### 1. 启用调试输出（默认）
```python
# 在 on_init() 方法中
self.debug_flag = True
```

### 2. 禁用调试输出
```python
# 在 on_init() 方法中
self.debug_flag = False
```

### 3. 动态切换调试输出
```python
# 在运行时切换
self.debug_flag = not self.debug_flag  # 切换状态
```

## 示例输出

### debug_flag = True 时的输出：
```
=== HandJointLinkage: on_init called ===
=== HandJointLinkage: on_play called ===
HandJointLinkage: Joint linkage timer started
HandJointLinkage: Processing frame #1
HandJointLinkage: Processing frame #2
HandJointLinkage: Processing frame #3
HandJointLinkage: Processing frame #4
HandJointLinkage: Processing frame #5
HandJointLinkage: Active - frame #60
HandJointLinkage: Active - frame #120
=== HandJointLinkage: on_pause called ===
HandJointLinkage: Joint linkage timer stopped
HandJointLinkage: Joint linkage loop cancelled
```

### debug_flag = False 时的输出：
```
(无调试输出，只有错误信息会显示)
```

## 测试脚本

使用 `test_debug_flag.py` 来测试调试标志功能：
- 演示如何动态切换 `debug_flag`
- 验证调试输出的开启/关闭效果

## 建议

### 开发阶段
- 设置 `self.debug_flag = True` 以便调试和监控脚本运行状态

### 生产环境
- 设置 `self.debug_flag = False` 以减少日志输出，提高性能

### 性能考虑
- 禁用调试输出可以减少 I/O 操作，略微提高性能
- 错误信息始终会输出，确保问题能够被及时发现

## 修改历史
- 添加了 `debug_flag` 控制机制
- 移除了所有 `carb.log_info()` 调用
- 保留了所有 `print()` 调用，但受 `debug_flag` 控制
- 保留了所有错误日志 `carb.log_error()` 调用
