#!/usr/bin/env python3
"""
诊断 BehaviorScript 问题的脚本
"""

import sys
import os

def diagnose_environment():
    """诊断 Python 环境"""
    print("=== Python 环境诊断 ===")
    print(f"Python 版本: {sys.version}")
    print(f"Python 路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查关键模块
    modules_to_check = [
        'omni.kit.scripting',
        'carb',
        'pxr',
        'omni.usd',
        'omni.timeline'
    ]
    
    print("\n=== 模块检查 ===")
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"✓ {module} - 可用")
        except ImportError as e:
            print(f"✗ {module} - 不可用: {e}")
    
    # 检查 BehaviorScript 类
    print("\n=== BehaviorScript 检查 ===")
    try:
        from omni.kit.scripting import BehaviorScript
        print(f"✓ BehaviorScript 类可用")
        print(f"✓ BehaviorScript 位置: {BehaviorScript.__module__}")
        
        # 检查方法
        methods = ['on_init', 'on_destroy', 'on_play', 'on_pause', 'on_stop', 'on_update']
        for method in methods:
            if hasattr(BehaviorScript, method):
                print(f"✓ {method} 方法存在")
            else:
                print(f"✗ {method} 方法不存在")
                
    except Exception as e:
        print(f"✗ BehaviorScript 不可用: {e}")
    
    # 检查日志系统
    print("\n=== 日志系统检查 ===")
    try:
        import carb
        carb.log_info("测试 carb.log_info")
        print("✓ carb.log_info 可用")
        
        carb.log_warn("测试 carb.log_warn")
        print("✓ carb.log_warn 可用")
        
        carb.log_error("测试 carb.log_error")
        print("✓ carb.log_error 可用")
        
    except Exception as e:
        print(f"✗ carb 日志系统不可用: {e}")
    
    # 测试文件写入
    print("\n=== 文件写入测试 ===")
    test_file = "/tmp/omniverse_diagnose.log"
    try:
        with open(test_file, "w") as f:
            f.write("诊断测试文件写入成功\n")
        print(f"✓ 文件写入成功: {test_file}")
        
        # 读取验证
        with open(test_file, "r") as f:
            content = f.read()
        print(f"✓ 文件读取成功: {content.strip()}")
        
    except Exception as e:
        print(f"✗ 文件操作失败: {e}")

def test_simple_behavior():
    """测试简单的 BehaviorScript"""
    print("\n=== 简单 BehaviorScript 测试 ===")
    
    try:
        from omni.kit.scripting import BehaviorScript
        import carb
        
        class TestBehavior(BehaviorScript):
            def on_init(self):
                print("TestBehavior: on_init 被调用")
                carb.log_info("TestBehavior: on_init 被调用")
                
        # 尝试创建实例
        behavior = TestBehavior()
        print("✓ TestBehavior 实例创建成功")
        
        # 尝试调用方法
        behavior.on_init()
        print("✓ on_init 方法调用成功")
        
    except Exception as e:
        print(f"✗ BehaviorScript 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_environment()
    test_simple_behavior()
    print("\n=== 诊断完成 ===")
    print("请将此输出发送给开发者以进行进一步诊断")
