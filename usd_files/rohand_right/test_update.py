from omni.kit.scripting import BehaviorScript
import carb

class TestUpdate(BehaviorScript):
    
    def on_init(self):
        print("TEST_UPDATE: on_init - Script loaded successfully")
        carb.log_info("TEST_UPDATE: on_init")
    
    def on_play(self):
        print("TEST_UPDATE: on_play - Timeline started")
        carb.log_info("TEST_UPDATE: on_play")
    
    def on_update(self):
        # 最简单的 on_update - 只打印一次
        if not hasattr(self, '_update_printed'):
            print("TEST_UPDATE: on_update - UPDATE METHOD IS BEING CALLED!")
            carb.log_info("TEST_UPDATE: on_update called")
            self._update_printed = True
            
            # 写入文件确认
            try:
                with open("/tmp/test_update_success.log", "w") as f:
                    f.write("on_update method was called successfully!\n")
            except:
                pass
    
    def on_pause(self):
        print("TEST_UPDATE: on_pause")
        carb.log_info("TEST_UPDATE: on_pause")
    
    def on_stop(self):
        print("TEST_UPDATE: on_stop")
        carb.log_info("TEST_UPDATE: on_stop")
        # 重置标志，下次 play 时可以再次打印
        if hasattr(self, '_update_printed'):
            delattr(self, '_update_printed')
    
    def on_destroy(self):
        print("TEST_UPDATE: on_destroy")
        carb.log_info("TEST_UPDATE: on_destroy")
