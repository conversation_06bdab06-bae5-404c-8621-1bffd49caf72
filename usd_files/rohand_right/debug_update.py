from omni.kit.scripting import BehaviorScript
import carb

class DebugUpdate(BehaviorScript):
    
    def on_init(self):
        print("DEBUG_UPDATE: on_init")
        carb.log_info("DEBUG_UPDATE: on_init")
    
    def on_play(self):
        print("DEBUG_UPDATE: on_play")
        carb.log_info("DEBUG_UPDATE: on_play")
    
    def on_update(self):
        # 尝试不同的操作，看哪个导致错误
        
        # 测试1: 只是 pass
        pass
        
        # 测试2: 简单的变量赋值（取消注释来测试）
        # x = 1
        
        # 测试3: 简单的打印（取消注释来测试）
        # print("DEBUG_UPDATE: on_update called")
        
        # 测试4: carb 日志（取消注释来测试）
        # carb.log_info("DEBUG_UPDATE: on_update called")
        
        # 测试5: 访问 self（取消注释来测试）
        # self.test_var = 1
        
        # 测试6: 数学运算（取消注释来测试）
        # result = 1 + 1
    
    def on_stop(self):
        print("DEBUG_UPDATE: on_stop")
        carb.log_info("DEBUG_UPDATE: on_stop")
    
    def on_destroy(self):
        print("DEBUG_UPDATE: on_destroy")
        carb.log_info("DEBUG_UPDATE: on_destroy")
