from omni.kit.scripting import BehaviorScript
import carb

class MinimalUpdate(BehaviorScript):
    
    def on_init(self):
        print("MINIMAL_UPDATE: on_init")
        carb.log_info("MINIMAL_UPDATE: on_init")
    
    def on_play(self):
        print("MINIMAL_UPDATE: on_play")
        carb.log_info("MINIMAL_UPDATE: on_play")
    
    def on_update(self):
        # 最简单的 on_update - 完全空的
        pass
    
    def on_stop(self):
        print("MINIMAL_UPDATE: on_stop")
        carb.log_info("MINIMAL_UPDATE: on_stop")
    
    def on_destroy(self):
        print("MINIMAL_UPDATE: on_destroy")
        carb.log_info("MINIMAL_UPDATE: on_destroy")
