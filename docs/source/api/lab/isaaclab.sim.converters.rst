﻿isaaclab.sim.converters
=======================

.. automodule:: isaaclab.sim.converters

  .. rubric:: Classes

  .. autosummary::

    AssetConverterBase
    AssetConverterBaseCfg
    MeshConverter
    MeshConverterCfg
    UrdfConverter
    UrdfConverterCfg

Asset Converter Base
--------------------

.. autoclass:: AssetConverterBase
    :members:

.. autoclass:: AssetConverterBaseCfg
    :members:
    :exclude-members: __init__

Mesh Converter
--------------

.. autoclass:: MeshConverter
    :members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: MeshConverterCfg
    :members:
    :inherited-members:
    :show-inheritance:
    :exclude-members: __init__


URDF Converter
--------------

.. autoclass:: UrdfConverter
    :members:
    :inherited-members:
    :show-inheritance:

.. autoclass:: UrdfConverterCfg
    :members:
    :inherited-members:
    :show-inheritance:
    :exclude-members: __init__
