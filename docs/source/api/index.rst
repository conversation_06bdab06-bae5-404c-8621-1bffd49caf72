API Reference
=============

This page gives an overview of all the modules and classes in the Isaac Lab extensions.

isaaclab extension
------------------

The following modules are available in the ``isaaclab`` extension:

.. currentmodule:: isaaclab

.. autosummary::
   :toctree: lab

   app
   actuators
   assets
   controllers
   devices
   envs
   managers
   markers
   scene
   sensors
   sim
   terrains
   utils

.. toctree::
   :hidden:

   lab/isaaclab.envs.mdp
   lab/isaaclab.envs.ui
   lab/isaaclab.sensors.patterns
   lab/isaaclab.sim.converters
   lab/isaaclab.sim.schemas
   lab/isaaclab.sim.spawners


isaaclab_rl extension
---------------------

The following wrappers are available in the ``isaaclab_rl`` extension:

.. currentmodule:: isaaclab_rl

.. toctree::
   :maxdepth: 2

   lab_rl/isaaclab_rl


isaaclab_mimic extension
------------------------

The following modules are available in the ``isaaclab_mimic`` extension:

.. currentmodule:: isaaclab_mimic

.. autosummary::
   :toctree: lab_mimic

   datagen
   envs


isaaclab_tasks extension
------------------------

This package ``isaaclab_tasks`` contains the tasks that are available in the Isaac Lab.
For more information, please refer to the :ref:`environments`.

It includes the following modules:

.. currentmodule:: isaaclab_tasks

.. autosummary::
   :toctree: lab_tasks

   utils
