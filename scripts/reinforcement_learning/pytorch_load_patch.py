#!/usr/bin/env python3
"""
PyTorch Load Compatibility Patch

This module provides a compatibility patch for PyTorch 2.6+ to handle the weights_only parameter
change in torch.load. In PyTorch 2.6, the default value of weights_only changed from False to True,
which breaks loading of older model checkpoints that contain numpy objects.

Usage:
    Import this module before any torch.load operations:
    
    import pytorch_load_patch  # This will automatically apply the patch
    
    Or apply manually:
    
    from pytorch_load_patch import apply_torch_load_patch
    apply_torch_load_patch()

The patch ensures backward compatibility by setting weights_only=False when not explicitly specified.
"""

import torch
import warnings


def apply_torch_load_patch():
    """Apply the torch.load compatibility patch."""
    if hasattr(torch.load, '_isaaclab_patched'):
        return  # Already patched
    
    # Store the original torch.load function
    _original_torch_load = torch.load
    
    def _patched_torch_load(*args, **kwargs):
        """Patched version of torch.load that maintains backward compatibility."""
        if 'weights_only' not in kwargs:
            # Set weights_only=False for backward compatibility
            kwargs['weights_only'] = False
            
            # Issue a warning for security awareness
            if not hasattr(_patched_torch_load, '_warning_issued'):
                warnings.warn(
                    "Using weights_only=False in torch.load for backward compatibility. "
                    "This may pose security risks if loading untrusted files. "
                    "Consider updating your checkpoints to be compatible with weights_only=True.",
                    UserWarning,
                    stacklevel=2
                )
                _patched_torch_load._warning_issued = True
        
        return _original_torch_load(*args, **kwargs)
    
    # Mark as patched to avoid double patching
    _patched_torch_load._isaaclab_patched = True
    
    # Replace torch.load with the patched version
    torch.load = _patched_torch_load


def remove_torch_load_patch():
    """Remove the torch.load compatibility patch."""
    if hasattr(torch.load, '_isaaclab_patched'):
        # This is a simplified removal - in practice, you'd need to store the original
        warnings.warn("Patch removal not fully implemented. Restart Python to use original torch.load.")


# Automatically apply the patch when this module is imported
apply_torch_load_patch()
