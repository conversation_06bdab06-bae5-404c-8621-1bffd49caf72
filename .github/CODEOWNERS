# Codeowners are designated by their GitHub username. They are
# the people who are responsible for reviewing and approving PRs
# that modify the files that match the pattern.
#
# Codeowners are not the same as contributors. They are not
# automatically added to the PR, but they will be requested to
# review the PR when it is created.
#
# As a general rule, the codeowners are the people who are
# most familiar with the code that the PR is modifying. If you
# are not sure who to add, ask in the issue or in the PR itself.
#
# The format of the file is as follows:
# <file pattern> <codeowners>


# App experience files
# These are the files that are used to launch the app with the correct settings and configurations
/apps/ @kellyguo11 @hhansen-bdai @Mayankm96

# Core Framework
/source/isaaclab/ @Mayankm96 @kellyguo11
/source/isaaclab/isaaclab/actuators @Mayankm96 @jtigue-bdai
/source/isaaclab/isaaclab/app @hhansen-bdai @kellyguo11
/source/isaaclab/isaaclab/assets @kellyguo11 @Mayankm96 @jtigue-bdai
/source/isaaclab/isaaclab/assets/deformable_object @kellyguo11 @Mayankm96 @masoudmoghani
/source/isaaclab/isaaclab/controllers @Mayankm96
/source/isaaclab/isaaclab/envs @kellyguo11 @Mayankm96
/source/isaaclab/isaaclab/envs/manager_based_* @Mayankm96 @jtigue-bdai
/source/isaaclab/isaaclab/envs/direct_* @kellyguo11
/source/isaaclab/isaaclab/managers @jtigue-bdai @Mayankm96
/source/isaaclab/isaaclab/sensors @pascal-roth @Mayankm96 @jtigue-bdai
/source/isaaclab/isaaclab/sensors/camera @kellyguo11 @pascal-roth
/source/isaaclab/isaaclab/sensors/contact_sensor @jtigue-bdai
/source/isaaclab/isaaclab/sensors/imu @jtigue-bdai @pascal-roth
/source/isaaclab/isaaclab/sensors/ray_caster @pascal-roth
/source/isaaclab/isaaclab/sim @Mayankm96 @jtigue-bdai
/source/isaaclab/isaaclab/sim/simulation_context.py @kellyguo11
/source/isaaclab/isaaclab/terrains @Mayankm96
/source/isaaclab/isaaclab/utils @Mayankm96 @jtigue-bdai
/source/isaaclab/isaaclab/utils/modifiers @jtigue-bdai
/source/isaaclab/isaaclab/utils/interpolation @jtigue-bdai
/source/isaaclab/isaaclab/utils/noise @jtigue-bdai @kellyguo11
/source/isaaclab/isaaclab/utils/warp @pascal-roth
/source/isaaclab/isaaclab/utils/assets.py @kellyguo11 @Mayankm96
/source/isaaclab/isaaclab/utils/math.py @jtigue-bdai @Mayankm96
/source/isaaclab/isaaclab/utils/configclass.py @Mayankm96

# RL Environment
/source/isaaclab_tasks/ @Mayankm96 @kellyguo11
/source/isaaclab_tasks/isaaclab_tasks/direct @kellyguo11
/source/isaaclab_tasks/isaaclab_tasks/manager_based @Mayankm96

# Assets
/source/isaaclab_assets/isaaclab_assets/ @pascal-roth

# Standalone Scripts
/scripts/demos/ @jtigue-bdai @kellyguo11 @Mayankm96
/scripts/environments/ @Mayankm96
/scripts/tools/ @jtigue-bdai @Mayankm96
/scripts/tutorials/ @jtigue-bdai @pascal-roth @kellyguo11 @Mayankm96
/scripts/reinforcement_learning/ @jtigue-bdai @kellyguo11 @Mayankm96
/scripts/imitation_learning/ @jtigue-bdai @kellyguo11 @Mayankm96

# Github Actions
# This list is for people wanting to be notified every time there's a change
# related to Github Actions
/.github/ @kellyguo11 @hhansen-bdai

# Visual Studio Code
/.vscode/ @hhansen-bdai @Mayankm96

# Infrastructure (Docker, Docs, Tools)
/docker/ @hhansen-bdai @pascal-roth
/docs/ @jtigue-bdai @kellyguo11 @Mayankm96
/tools/ @hhansen-bdai
/isaaclab.* @hhansen-bdai @Mayankm96 @kellyguo11
