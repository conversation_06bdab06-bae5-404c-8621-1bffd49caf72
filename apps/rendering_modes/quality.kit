rtx.translucency.enabled = true

rtx.reflections.enabled = true
rtx.reflections.denoiser.enabled = true

# this will be ignored when RR (dldenoiser) is enabled
# rtx.directLighting.sampledLighting.denoisingTechnique = 0
rtx.directLighting.sampledLighting.enabled = true

rtx.sceneDb.ambientLightIntensity = 1.0

rtx.shadows.enabled = true

rtx.indirectDiffuse.enabled = true
rtx.indirectDiffuse.denoiser.enabled = true

# rtx.domeLight.upperLowerStrategy = 4

rtx.ambientOcclusion.enabled = true
rtx.ambientOcclusion.denoiserMode = 0

rtx.raytracing.subpixel.mode = 1
rtx.raytracing.cached.enabled = true

# DLSS frame gen does not yet support tiled camera well
rtx-transient.dlssg.enabled = false
rtx-transient.dldenoiser.enabled = true

# Set the DLSS model
rtx.post.dlss.execMode = 2 # can be 0 (Performance), 1 (Balanced), 2 (Quality), or 3 (Auto)

# Avoids replicator warning
rtx.pathtracing.maxSamplesPerLaunch = 1000000

# Avoids silent trimming of tiles
rtx.viewTile.limit = 1000000
